using AutoMapper;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Helpers;
using Marketplaces.Api.Models;
using Marketplaces.Api.Models.Identity;
using Marketplaces.Api.Requests;
using Marketplaces.Api.Responses;
using Marketplaces.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Net.Mime;
using System.Security.Claims;
using System.Text;
using System.Text.Json;

namespace Marketplaces.Api.Controllers;

[Route("[controller]")]
[ApiController]
[Consumes(MediaTypeNames.Application.Json)]
[Produces(MediaTypeNames.Application.Json)]
public class AccountController(UserManager<ApplicationUser> userManager,
    IOptions<AppSettings> options, DatabaseContext databaseContext, IEmailSender emailSender, IMapper mapper,
    PaymentsService paymentsService) : ControllerBase
{
    private readonly AppSettings _appSettings = options.Value;

    [HttpGet]
    [Authorize]
    public async Task<IActionResult> GetUser()
    {
        var user = await userManager.GetUserAsync(User);
        if (user == null)
        {
            throw new UnauthorizedException();
        }

        var shop = await databaseContext.Shops.Include(s => s.ShopUsers)
            .Include(s => s.Subscriptions
                .Where(su => su.EndDate >= DateTime.UtcNow))
            .FirstOrDefaultAsync(s => s.ShopUsers.Select(su => su.UserId).Contains(user.Id));
        if (shop is null)
        {
            throw new NotFoundException();
        }

        if (shop.FindSubscription() == null)
        {
            var unprocessedPayments = await databaseContext.Payments
                .Where(p => p.ShopId == shop.Id && p.ExternalId != null && p.ConfirmedAt == null &&
                            p.CanceledAt == null)
                .Select(p => p.ExternalId!.Value).ToListAsync();

            await paymentsService.ProcessPayments(unprocessedPayments);
        }

        var dto = mapper.Map<UserDto>(user, c => { c.Items["Shop"] = shop; });
        return Ok(dto);
    }

    [HttpPost("register")]
    public async Task<IActionResult> Register([FromBody] RegisterBody body)
    {
        int? partnerId = null;
        if (!string.IsNullOrEmpty(body.PromoCode))
        {
            var partner = await databaseContext.Set<Partner>()
                .FirstOrDefaultAsync(p => p.PromoCode.ToLower() == body.PromoCode.ToLower().Trim());
            if (partner == null)
            {
                throw new BadRequestException("Промокод не найден.");
            }

            partnerId = partner.Id;
        }

        var daysToAdd = partnerId.HasValue ? 14 : 7;
        var user = new ApplicationUser(body.Email,
            body.FirstName,
            body.LastName,
            body.MiddleName,
            body.IsConsentGiven,
            partnerId);

        var result = await userManager.CreateAsync(user, body.Password);
        if (!result.Succeeded)
        {
            return BadRequest(result.Errors);
        }

        var shop = new Shop(body.ShopName ?? string.Empty, user.Id);
        shop.CrateOrProlongSubscription(DateTimeOffset.UtcNow.AddDays(daysToAdd));
        await databaseContext.AddAsync(shop);
        await databaseContext.SaveChangesAsync();
        var confirmationToken = await userManager.GenerateEmailConfirmationTokenAsync(user);
        var text = BuildEmailConfirmationText(user.Id, user.FirstName, confirmationToken);
        await emailSender.SendEmailAsync(user.Email!, "Подтверждение email для сервиса MerchantBox", text);
        var token = GenerateJwtToken(user);
        return Ok(new { token = token });
    }

    [HttpPost("login")]
    [ProducesResponseType(typeof(LoginModelDto), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(ProblemDetails), (int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    public async Task<ActionResult<LoginModelDto>> Login([FromBody] LoginBody body)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var user = await userManager.FindByEmailAsync(body.Email!);
        if (user != null && await userManager.CheckPasswordAsync(user, body.Password!))
        {
            // Генерация JWT токена
            var token = GenerateJwtToken(user);
            return Ok(new { token });
        }

        return Unauthorized();
    }

    // Генерация JWT токена
    private string GenerateJwtToken(ApplicationUser user)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_appSettings.TokenSecret));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(ClaimTypes.NameIdentifier, user.Id),
            new Claim("ss", user.SecurityStamp ?? string.Empty),
        };

        var token = new JwtSecurityToken(
            claims: claims,
            expires: DateTime.Now.AddDays(_appSettings.TokenLifetimeInDays),
            signingCredentials: creds);

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    [Authorize]
    [HttpPut("update-password")]
    public async Task<IActionResult> UpdatePassword([FromBody] UpdatePasswordBody body)
    {
        // Получаем текущего пользователя
        var user = await userManager.GetUserAsync(User);
        if (user == null)
        {
            return Unauthorized(); // Пользователь не найден
        }

        // Проверяем, что старый пароль верен
        var passwordCheck = await userManager.CheckPasswordAsync(user, body.OldPassword);
        if (!passwordCheck)
        {
            return BadRequest(new { message = "Старый пароль неверен." });
        }

        // Обновляем пароль
        var result = await userManager.ChangePasswordAsync(user, body.OldPassword, body.NewPassword);
        if (!result.Succeeded)
        {
            return BadRequest(result.Errors); // Возвращаем ошибки, если есть
        }

        await userManager.UpdateSecurityStampAsync(user);
        var token = GenerateJwtToken(user);

        return Ok(new { Token = token });
    }

    [HttpPost("update-email")]
    [Authorize]
    public async Task<IActionResult> UpdateEmail([FromBody] UpdateEmailBody model)
    {
        // Находим пользователя по ID или текущему контексту
        var user = await userManager.GetUserAsync(User);
        if (user == null)
        {
            throw new NotFoundException();
        }

        var emailExists = await userManager.FindByEmailAsync(model.NewEmail);
        if (emailExists != null)
        {
            throw new BadRequestException("Email уже используется.");
        }

        var token = await userManager.GenerateChangeEmailTokenAsync(user, model.NewEmail);
        var message = BuildEmailChangeConfirmationText(user.Id, token, model.NewEmail);
        // Отправка ссылки на новый email
        await emailSender.SendEmailAsync(model.NewEmail, "Подтверждение нового email", message);
        return Ok(new { message = "Confirmation email sent. Please check your new email to confirm." });
    }

    [HttpPatch]
    [Authorize]
    public async Task<IActionResult> UpdateInfo(JsonDocument json)
    {
        var user = await userManager.GetUserAsync(User);
        if (user == null)
        {
            return Unauthorized();
        }

        var body = new UpdateAccountBody()
        {
            FirstName = user.FirstName,
            LastName = user.LastName,
            MiddleName = user.MiddleName
        };

        body.Patch(json.RootElement);
        user.UpdateInfo(body.FirstName, body.LastName, body.MiddleName);
        await userManager.UpdateAsync(user);
        return Ok();
    }

    [HttpPost("email-change-confirmation")]
    public async Task<IActionResult> ConfirmChangeEmail(EmailChangeBody body)
    {
        if (body.UserId == null || body.Token == null || body.NewEmail == null)
        {
            throw new BadRequestException("Invalid confirmation data.");
        }

        var user = await userManager.FindByIdAsync(body.UserId);
        if (user == null)
        {
            throw new NotFoundException();
        }

        var result = await userManager.ChangeEmailAsync(user, body.NewEmail, body.Token);
        if (!result.Succeeded)
        {
            throw new BadRequestException("Error confirming new email.");
        }

        var token = GenerateJwtToken(user);
        return Ok(new { message = "Email updated successfully.", token = token });
    }

    [HttpPost("email-confirmation")]
    public async Task<IActionResult> ConfirmEmail(ConfirmEmailBody body)
    {
        if (body.UserId == null || body.Token == null)
        {
            throw new BadRequestException("Invalid confirmation data.");
        }

        var user = await userManager.FindByIdAsync(body.UserId);
        if (user == null)
        {
            throw new NotFoundException();
        }

        var result = await userManager.ConfirmEmailAsync(user, body.Token);
        if (result.Succeeded)
        {
            return Ok();
        }

        throw new BadRequestException("Email не подтвержден.");
    }

    [HttpPost("reset-password")]
    public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordBody body)
    {
        var user = await userManager.FindByEmailAsync(body.Email);
        if (user == null)
        {
            throw new BadRequestException("Пользователь не найден.");
        }

        var token = await userManager.GeneratePasswordResetTokenAsync(user);
        var text = $"Для сброса пароля перейдите по ссылке: нажмите сюда: " +
                   $"<a href=\"{_appSettings.ApplicationUrl}/login/reset-password?type=password&userId={user.Id}&token={Uri.EscapeDataString(token)}\">Клик</a>";
        await emailSender.SendEmailAsync(user.Email!, "Сброс пароля", text);
        return Ok();
    }

    [HttpPost("reset-password-confirmation")]
    public async Task<IActionResult> ResetPasswordConfirmation(ResetPasswordConfirmationBody body)
    {
        var user = await userManager.FindByIdAsync(body.UserId);
        if (user == null)
        {
            throw new BadRequestException("Пользователь не найден.");
        }

        var result = await userManager.ResetPasswordAsync(user, body.Token, body.NewPassword);
        if (result.Succeeded)
        {
            return Ok();
        }

        throw new BadRequestException("Ошибка сброса пароля.");
    }

    [HttpPost("repeat-email-confirmation")]
    public async Task<IActionResult> RepeatEmailConfirmation()
    {
        var user = await userManager.GetUserAsync(User);
        if (user == null)
        {
            return Unauthorized();
        }

        var confirmationToken = await userManager.GenerateEmailConfirmationTokenAsync(user);
        var text = BuildEmailConfirmationText(user.Id, user.FirstName, confirmationToken);
        await emailSender.SendEmailAsync(user.Email!, "Подтверждение Email", text);
        return Ok();
    }

    private string BuildEmailConfirmationText(string userId, string userName, string confirmationToken)
    {
        var confirmationLink = new UriBuilder(_appSettings.ApplicationUrl +
                                              $"/login/email-confirmation?userId={userId}&token={Uri.EscapeDataString(confirmationToken)}").ToString();

        var sb = new StringBuilder();
        sb.AppendLine($"<p>Здравствуйте, {userName}!</p>");
        sb.AppendLine("<p>Для завершения регистрации в сервисе MerchantBox подтвердите, пожалуйста, ваш email.</p>");
        sb.AppendLine("<p>Нажмите на кнопку ниже, чтобы подтвердить адрес:</p>");
        sb.AppendLine($"<p><a href=\"{confirmationLink}\" style=\"display:inline-block;padding:10px 20px;color:#fff;background-color:#007bff;text-decoration:none;border-radius:5px;\">Подтвердить email</a></p>");
        sb.AppendLine("<p>Если кнопка не работает, скопируйте и вставьте ссылку в браузер:</p>");
        sb.AppendLine($"<p><a href=\"{confirmationLink}\">{confirmationLink}</a></p>");
        sb.AppendLine("<hr>");
        sb.AppendLine("<p><strong>Важно:</strong></p>");
        sb.AppendLine("<p>— Если вы не регистрировались в сервисе, просто проигнорируйте это письмо.</p>");
        sb.AppendLine("<p>После подтверждения email вы сможете пользоваться всеми функциями нашего сервиса. Если возникнут вопросы, напишите нам: <a href=\"mailto:<EMAIL>\"><EMAIL></a></p>");
        sb.AppendLine("<p>Спасибо, что выбрали нас!</p>");
        sb.AppendLine("<p>С уважением,<br>Команда MerchantBox</p>");

        return sb.ToString();
    }

    private string BuildEmailChangeConfirmationText(string userId, string confirmationToken, string newEmail)
    {
        var url = _appSettings.ApplicationUrl +
                  $"/login/email-confirmation?userId={userId}&token={Uri.EscapeDataString(confirmationToken)}&newEmail={Uri.EscapeDataString(newEmail)}";
        return $"Для подтверждения изменения Email перейдите сюда: <a href=\"{url}\">Клик</a>";
    }
}