using System.Text.Json.Serialization;

namespace Marketplaces.Api.Responses;

public class NestedPriceDto
{
    private const int DecimalPlaces = 2;

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? BasePrice { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? Price { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? MarketingPrice { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? MarketingSellerPrice { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? MinPrice { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? OldPrice { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public decimal? DiscountPercent { get; set; }

    public decimal? MinimumProfitPercentage { get; set; }

    public decimal? MinimumRevenue { get; set; }

    public string? Size { get; set; }

    public decimal? RedemptionPercent { get; set; }

    public decimal? EstimatedTax { get; set; }

    public string? Sku { get; set; }

    public string? Code { get; set; }

    public decimal? SalePrice { get; set; }

    public decimal? BankCommissionAmount { get; set; }

    public decimal? MarketplaceCommissionAmount { get; set; }

    public decimal? Revenue { get; set; }

    public decimal? Profit { get; set; }

    public decimal? ProfitPercentage { get; set; }

    /// <summary>
    /// Calculates profit-related fields for this marketplace price
    /// </summary>
    /// <param name="purchasePrice">The purchase price of the item</param>
    /// <param name="bankTax">Bank commission percentage</param>
    /// <param name="marketplaceTax">Marketplace commission percentage</param>
    public void CalculateProfitFields(decimal? purchasePrice, decimal? bankTax, decimal? marketplaceTax)
    {
        if (BasePrice is null)
            return;

        decimal? salePrice = SalePrice;
        if (salePrice is null)
        {
            salePrice = BasePrice.Value;
            if (DiscountPercent.HasValue)
            {
                var discountMultiplier = 1 - (DiscountPercent.Value / 100);
                salePrice = BasePrice.Value * discountMultiplier;
            }
        }
        SalePrice = Math.Round(salePrice.Value, DecimalPlaces);

        if (purchasePrice is null || bankTax is null || marketplaceTax is null)
            return;
        var bankCommissionAmount = salePrice * (bankTax.Value / 100);
        var marketplaceCommissionAmount = salePrice * (marketplaceTax.Value / 100);
        var revenue = salePrice - bankCommissionAmount - marketplaceCommissionAmount;
        var profit = revenue - purchasePrice.Value;
        var profitPercentage = purchasePrice.Value != 0 ? profit / purchasePrice.Value * 100 : 0;

        BankCommissionAmount = Math.Round(bankCommissionAmount.Value, DecimalPlaces);
        MarketplaceCommissionAmount = Math.Round(marketplaceCommissionAmount.Value, DecimalPlaces);
        Revenue = Math.Round(revenue.Value, DecimalPlaces);
        Profit = Math.Round(profit.Value, DecimalPlaces);
        ProfitPercentage = Math.Round(profitPercentage.Value, DecimalPlaces);
    }
}